import request from '@/utils/request'

// 微信授权登录
export function wechatLogin(code) {
  return request({
    url: '/miniapp/auth/wechat',
    method: 'post',
    data: { code }
  })
}

// 手机号一键登录
export function phoneLogin(phone, code) {
  return request({
    url: '/miniapp/auth/phone',
    method: 'post',
    data: { phone, code }
  })
}

// 获取用户信息
export function getUserInfo(seekerId) {
  return request({
    url: '/miniapp/user/' + seekerId,
    method: 'get'
  })
}

// 更新用户信息
export function updateUserInfo(data) {
  return request({
    url: '/miniapp/user',
    method: 'put',
    data: data
  })
}

// 获取职位列表
export function getPositions(params) {
  return request({
    url: '/miniapp/positions',
    method: 'get',
    params: params
  })
}

// 获取职位详情
export function getPositionDetail(positionId) {
  return request({
    url: '/miniapp/position/' + positionId,
    method: 'get'
  })
}

// 获取热门职位
export function getHotPositions(limit = 10) {
  return request({
    url: '/miniapp/positions/hot',
    method: 'get',
    params: { limit }
  })
}

// 获取最新职位
export function getLatestPositions(limit = 10) {
  return request({
    url: '/miniapp/positions/latest',
    method: 'get',
    params: { limit }
  })
}

// 查看联系方式
export function viewContact(positionId, seekerId) {
  return request({
    url: '/miniapp/position/' + positionId + '/contact',
    method: 'post',
    params: { seekerId }
  })
}

// 获取会员套餐列表
export function getMemberPackages() {
  return request({
    url: '/miniapp/packages',
    method: 'get'
  })
}

// 创建订单
export function createOrder(data) {
  return request({
    url: '/miniapp/order/create',
    method: 'post',
    data: data
  })
}

// 发起微信支付
export function wechatPay(orderNo, openid) {
  return request({
    url: '/miniapp/pay/wechat',
    method: 'post',
    data: { orderNo, openid }
  })
}

// 获取用户订单列表
export function getUserOrders(seekerId) {
  return request({
    url: '/miniapp/orders/' + seekerId,
    method: 'get'
  })
}

// 获取用户消费记录
export function getUserRecords(seekerId) {
  return request({
    url: '/miniapp/records/' + seekerId,
    method: 'get'
  })
}

// 检查用户会员状态
export function getMemberStatus(seekerId) {
  return request({
    url: '/miniapp/member/status/' + seekerId,
    method: 'get'
  })
}

// 获取地区列表
export function getRegions(parentCode) {
  return request({
    url: '/miniapp/regions',
    method: 'get',
    params: { parentCode }
  })
}
