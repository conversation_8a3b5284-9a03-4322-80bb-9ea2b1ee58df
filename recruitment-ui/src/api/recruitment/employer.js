import request from '@/utils/request'

// 查询商家列表
export function listEmployer(query) {
  return request({
    url: '/recruitment/employer/list',
    method: 'get',
    params: query
  })
}

// 查询商家详细
export function getEmployer(employerId) {
  return request({
    url: '/recruitment/employer/' + employerId,
    method: 'get'
  })
}

// 新增商家
export function addEmployer(data) {
  return request({
    url: '/recruitment/employer',
    method: 'post',
    data: data
  })
}

// 修改商家
export function updateEmployer(data) {
  return request({
    url: '/recruitment/employer',
    method: 'put',
    data: data
  })
}

// 删除商家
export function delEmployer(employerId) {
  return request({
    url: '/recruitment/employer/' + employerId,
    method: 'delete'
  })
}

// 审核商家
export function auditEmployer(data) {
  return request({
    url: '/recruitment/employer/audit',
    method: 'put',
    data: data
  })
}

// 更新商家状态
export function updateEmployerStatus(employerId, status) {
  return request({
    url: '/recruitment/employer/status/' + employerId + '/' + status,
    method: 'put'
  })
}

// 获取商家统计信息
export function getEmployerStatistics() {
  return request({
    url: '/recruitment/employer/statistics',
    method: 'get'
  })
}

// 查询待审核商家列表
export function getPendingAuditEmployers() {
  return request({
    url: '/recruitment/employer/pendingAudit',
    method: 'get'
  })
}

// 检查登录账号是否存在
export function checkLoginAccount(loginAccount) {
  return request({
    url: '/recruitment/employer/checkAccount/' + loginAccount,
    method: 'get'
  })
}

// 检查联系电话是否存在
export function checkContactPhone(contactPhone) {
  return request({
    url: '/recruitment/employer/checkPhone/' + contactPhone,
    method: 'get'
  })
}

// 重置商家密码
export function resetEmployerPassword(employerId, password) {
  return request({
    url: '/recruitment/employer/resetPassword',
    method: 'put',
    data: {
      employerId: employerId,
      loginPassword: password
    }
  })
}

// 商家注册（公开接口）
export function registerEmployer(data) {
  return request({
    url: '/recruitment/employer/register',
    method: 'post',
    data: data
  })
}

// 商家登录（公开接口）
export function loginEmployer(data) {
  return request({
    url: '/recruitment/employer/login',
    method: 'post',
    data: data
  })
}
