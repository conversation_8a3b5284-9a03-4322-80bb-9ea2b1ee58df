import request from '@/utils/request'

// 查询求职者列表
export function listSeeker(query) {
  return request({
    url: '/recruitment/seeker/list',
    method: 'get',
    params: query
  })
}

// 查询求职者详细
export function getSeeker(seekerId) {
  return request({
    url: '/recruitment/seeker/' + seekerId,
    method: 'get'
  })
}

// 新增求职者
export function addSeeker(data) {
  return request({
    url: '/recruitment/seeker',
    method: 'post',
    data: data
  })
}

// 修改求职者
export function updateSeeker(data) {
  return request({
    url: '/recruitment/seeker',
    method: 'put',
    data: data
  })
}

// 删除求职者
export function delSeeker(seekerId) {
  return request({
    url: '/recruitment/seeker/' + seekerId,
    method: 'delete'
  })
}

// 更新求职者状态
export function updateSeekerStatus(seekerId, status) {
  return request({
    url: '/recruitment/seeker/status/' + seekerId + '/' + status,
    method: 'put'
  })
}

// 获取求职者统计信息
export function getSeekerStatistics() {
  return request({
    url: '/recruitment/seeker/statistics',
    method: 'get'
  })
}

// 检查求职者是否为会员
export function checkSeekerMember(seekerId) {
  return request({
    url: '/recruitment/seeker/member/' + seekerId,
    method: 'get'
  })
}

// 获取求职者剩余查看次数
export function getSeekerViewCount(seekerId) {
  return request({
    url: '/recruitment/seeker/viewCount/' + seekerId,
    method: 'get'
  })
}

// 手动增加查看次数
export function addSeekerViewCount(seekerId, count) {
  return request({
    url: '/recruitment/seeker/addViewCount/' + seekerId + '/' + count,
    method: 'put'
  })
}

// 手动升级会员
export function upgradeSeekerMember(data) {
  return request({
    url: '/recruitment/seeker/upgradeMember',
    method: 'put',
    data: data
  })
}
