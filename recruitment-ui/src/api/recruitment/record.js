import request from '@/utils/request'

// 查询查看记录列表
export function listRecord(query) {
  return request({
    url: '/recruitment/record/list',
    method: 'get',
    params: query
  })
}

// 查询查看记录详细
export function getRecord(recordId) {
  return request({
    url: '/recruitment/record/' + recordId,
    method: 'get'
  })
}

// 新增查看记录
export function addRecord(data) {
  return request({
    url: '/recruitment/record',
    method: 'post',
    data: data
  })
}

// 修改查看记录
export function updateRecord(data) {
  return request({
    url: '/recruitment/record',
    method: 'put',
    data: data
  })
}

// 删除查看记录
export function delRecord(recordId) {
  return request({
    url: '/recruitment/record/' + recordId,
    method: 'delete'
  })
}

// 根据求职者ID查询查看记录
export function getRecordsBySeeker(seekerId) {
  return request({
    url: '/recruitment/record/seeker/' + seekerId,
    method: 'get'
  })
}

// 根据职位ID查询查看记录
export function getRecordsByPosition(positionId) {
  return request({
    url: '/recruitment/record/position/' + positionId,
    method: 'get'
  })
}

// 根据商家ID查询查看记录
export function getRecordsByEmployer(employerId) {
  return request({
    url: '/recruitment/record/employer/' + employerId,
    method: 'get'
  })
}

// 获取查看记录统计信息
export function getRecordStatistics() {
  return request({
    url: '/recruitment/record/statistics',
    method: 'get'
  })
}

// 获取热门职位查看统计
export function getHotPositionViewStats(limit = 10) {
  return request({
    url: '/recruitment/record/hotPositions',
    method: 'get',
    params: { limit }
  })
}

// 获取用户消费统计
export function getUserConsumptionStats(seekerId) {
  return request({
    url: '/recruitment/record/consumption/' + seekerId,
    method: 'get'
  })
}
