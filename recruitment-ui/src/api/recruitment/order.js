import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/recruitment/order/list',
    method: 'get',
    params: query
  })
}

// 查询订单详细
export function getOrder(orderId) {
  return request({
    url: '/recruitment/order/' + orderId,
    method: 'get'
  })
}

// 新增订单
export function addOrder(data) {
  return request({
    url: '/recruitment/order',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateOrder(data) {
  return request({
    url: '/recruitment/order',
    method: 'put',
    data: data
  })
}

// 删除订单
export function delOrder(orderId) {
  return request({
    url: '/recruitment/order/' + orderId,
    method: 'delete'
  })
}

// 取消订单
export function cancelOrder(orderId) {
  return request({
    url: '/recruitment/order/cancel/' + orderId,
    method: 'put'
  })
}

// 申请退款
export function refundOrder(orderId, refundReason) {
  return request({
    url: '/recruitment/order/refund',
    method: 'put',
    data: {
      orderId: orderId,
      refundReason: refundReason
    }
  })
}

// 获取订单统计信息
export function getOrderStatistics() {
  return request({
    url: '/recruitment/order/statistics',
    method: 'get'
  })
}

// 查询待支付订单
export function getPendingPaymentOrders() {
  return request({
    url: '/recruitment/order/pendingPayment',
    method: 'get'
  })
}

// 查询已支付订单
export function getPaidOrders() {
  return request({
    url: '/recruitment/order/paid',
    method: 'get'
  })
}

// 根据求职者ID查询订单列表
export function getOrdersBySeeker(seekerId) {
  return request({
    url: '/recruitment/order/seeker/' + seekerId,
    method: 'get'
  })
}

// 根据订单号查询订单
export function getOrderByNo(orderNo) {
  return request({
    url: '/recruitment/order/no/' + orderNo,
    method: 'get'
  })
}
