import request from '@/utils/request'

// 查询职位列表
export function listPosition(query) {
  return request({
    url: '/recruitment/position/list',
    method: 'get',
    params: query
  })
}

// 查询职位详细
export function getPosition(positionId) {
  return request({
    url: '/recruitment/position/' + positionId,
    method: 'get'
  })
}

// 新增职位
export function addPosition(data) {
  return request({
    url: '/recruitment/position',
    method: 'post',
    data: data
  })
}

// 修改职位
export function updatePosition(data) {
  return request({
    url: '/recruitment/position',
    method: 'put',
    data: data
  })
}

// 删除职位
export function delPosition(positionId) {
  return request({
    url: '/recruitment/position/' + positionId,
    method: 'delete'
  })
}

// 审核职位
export function auditPosition(data) {
  return request({
    url: '/recruitment/position/audit',
    method: 'put',
    data: data
  })
}

// 批量审核职位
export function batchAuditPositions(positionIds, auditStatus, auditRemark) {
  return request({
    url: '/recruitment/position/batchAudit',
    method: 'put',
    data: {
      auditStatus: auditStatus,
      auditRemark: auditRemark
    },
    params: {
      positionIds: positionIds
    }
  })
}

// 更新职位状态
export function updatePositionStatus(positionId, publishStatus) {
  return request({
    url: '/recruitment/position/status/' + positionId + '/' + publishStatus,
    method: 'put'
  })
}

// 批量更新职位状态
export function batchUpdatePositionStatus(positionIds, publishStatus) {
  return request({
    url: '/recruitment/position/batchStatus',
    method: 'put',
    params: {
      positionIds: positionIds,
      publishStatus: publishStatus
    }
  })
}

// 置顶/取消置顶职位
export function updatePositionTop(positionId, isTop) {
  return request({
    url: '/recruitment/position/top/' + positionId + '/' + isTop,
    method: 'put'
  })
}

// 设置紧急职位
export function updatePositionUrgent(positionId, isUrgent) {
  return request({
    url: '/recruitment/position/urgent/' + positionId + '/' + isUrgent,
    method: 'put'
  })
}

// 获取职位统计信息
export function getPositionStatistics() {
  return request({
    url: '/recruitment/position/statistics',
    method: 'get'
  })
}

// 查询待审核职位列表
export function getPendingAuditPositions() {
  return request({
    url: '/recruitment/position/pendingAudit',
    method: 'get'
  })
}

// 查询热门职位列表
export function getHotPositions(limit = 10) {
  return request({
    url: '/recruitment/position/hot',
    method: 'get',
    params: { limit }
  })
}

// 查询最新职位列表
export function getLatestPositions(limit = 10) {
  return request({
    url: '/recruitment/position/latest',
    method: 'get',
    params: { limit }
  })
}

// 根据地区查询职位列表
export function getPositionsByRegion(regionCode) {
  return request({
    url: '/recruitment/position/region/' + regionCode,
    method: 'get'
  })
}

// 搜索职位
export function searchPositions(keyword, regionCode) {
  return request({
    url: '/recruitment/position/search',
    method: 'get',
    params: {
      keyword: keyword,
      regionCode: regionCode
    }
  })
}

// 根据商家ID查询职位列表
export function getPositionsByEmployer(employerId) {
  return request({
    url: '/recruitment/position/employer/' + employerId,
    method: 'get'
  })
}
