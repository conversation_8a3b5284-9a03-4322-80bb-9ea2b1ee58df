<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="职位标题" prop="positionTitle">
        <el-input
          v-model="queryParams.positionTitle"
          placeholder="请输入职位标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="企业名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布状态" prop="publishStatus">
        <el-select v-model="queryParams.publishStatus" placeholder="请选择发布状态" clearable>
          <el-option label="草稿" value="0" />
          <el-option label="待审核" value="1" />
          <el-option label="已发布" value="2" />
          <el-option label="已下线" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="queryParams.auditStatus" placeholder="请选择审核状态" clearable>
          <el-option label="待审核" value="0" />
          <el-option label="已通过" value="1" />
          <el-option label="已拒绝" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="工作性质" prop="workNature">
        <el-select v-model="queryParams.workNature" placeholder="请选择工作性质" clearable>
          <el-option label="全职" value="全职" />
          <el-option label="兼职" value="兼职" />
          <el-option label="实习" value="实习" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['recruitment:position:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['recruitment:position:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['recruitment:position:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleBatchAudit"
          v-hasPermi="['recruitment:position:audit']"
        >批量审核</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['recruitment:position:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="positionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="职位ID" align="center" prop="positionId" width="80" />
      <el-table-column label="职位标题" align="center" prop="positionTitle" :show-overflow-tooltip="true" />
      <el-table-column label="企业名称" align="center" prop="companyName" :show-overflow-tooltip="true" />
      <el-table-column label="工作性质" align="center" prop="workNature" width="80" />
      <el-table-column label="薪资范围" align="center" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.salaryMin && scope.row.salaryMax">
            {{ scope.row.salaryMin }}-{{ scope.row.salaryMax }}{{ scope.row.salaryUnit }}
          </span>
          <span v-else>面议</span>
        </template>
      </el-table-column>
      <el-table-column label="地区" align="center" prop="regionName" />
      <el-table-column label="发布状态" align="center" prop="publishStatus" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.publishStatus == 0" type="info">草稿</el-tag>
          <el-tag v-else-if="scope.row.publishStatus == 1" type="warning">待审核</el-tag>
          <el-tag v-else-if="scope.row.publishStatus == 2" type="success">已发布</el-tag>
          <el-tag v-else-if="scope.row.publishStatus == 3" type="danger">已下线</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="auditStatus" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.auditStatus == 0" type="warning">待审核</el-tag>
          <el-tag v-else-if="scope.row.auditStatus == 1" type="success">已通过</el-tag>
          <el-tag v-else-if="scope.row.auditStatus == 2" type="danger">已拒绝</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="浏览/申请" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.viewCount || 0 }}/{{ scope.row.applyCount || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标签" align="center" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isTop == 1" type="danger" size="mini">置顶</el-tag>
          <el-tag v-if="scope.row.isUrgent == 1" type="warning" size="mini">紧急</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="发布时间" align="center" prop="publishTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['recruitment:position:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['recruitment:position:edit']"
          >修改</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['recruitment:position:audit']">
            <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="handleAudit" icon="el-icon-circle-check">审核</el-dropdown-item>
              <el-dropdown-item command="handleStatus" icon="el-icon-switch-button">状态</el-dropdown-item>
              <el-dropdown-item command="handleTop" icon="el-icon-top">置顶</el-dropdown-item>
              <el-dropdown-item command="handleUrgent" icon="el-icon-warning">紧急</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改职位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="职位标题" prop="positionTitle">
              <el-input v-model="form.positionTitle" placeholder="请输入职位标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位类型" prop="positionType">
              <el-input v-model="form.positionType" placeholder="请输入职位类型" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属行业" prop="industry">
              <el-input v-model="form.industry" placeholder="请输入所属行业" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作性质" prop="workNature">
              <el-select v-model="form.workNature" placeholder="请选择工作性质">
                <el-option label="全职" value="全职" />
                <el-option label="兼职" value="兼职" />
                <el-option label="实习" value="实习" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="最低薪资" prop="salaryMin">
              <el-input-number v-model="form.salaryMin" :min="0" :precision="2" placeholder="最低薪资" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最高薪资" prop="salaryMax">
              <el-input-number v-model="form.salaryMax" :min="0" :precision="2" placeholder="最高薪资" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="薪资单位" prop="salaryUnit">
              <el-select v-model="form.salaryUnit" placeholder="请选择薪资单位">
                <el-option label="月" value="月" />
                <el-option label="日" value="日" />
                <el-option label="小时" value="小时" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="工作经验" prop="workExperience">
              <el-input v-model="form.workExperience" placeholder="请输入工作经验要求" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学历要求" prop="education">
              <el-input v-model="form.education" placeholder="请输入学历要求" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="工作地址" prop="workAddress">
          <el-input v-model="form.workAddress" placeholder="请输入工作地址" />
        </el-form-item>
        <el-form-item label="职位描述" prop="jobDescription">
          <el-input v-model="form.jobDescription" type="textarea" :rows="4" placeholder="请输入职位描述" />
        </el-form-item>
        <el-form-item label="职位要求" prop="jobRequirements">
          <el-input v-model="form.jobRequirements" type="textarea" :rows="4" placeholder="请输入职位要求" />
        </el-form-item>
        <el-form-item label="福利待遇" prop="welfareBenefits">
          <el-input v-model="form.welfareBenefits" type="textarea" :rows="3" placeholder="请输入福利待遇" />
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="form.contactPerson" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="微信号" prop="contactWechat">
              <el-input v-model="form.contactWechat" placeholder="请输入微信号" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="职位审核" :visible.sync="auditOpen" width="500px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核状态" prop="auditStatus">
          <el-radio-group v-model="auditForm.auditStatus">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" placeholder="请输入审核备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAudit">确 定</el-button>
        <el-button @click="cancelAudit">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量审核对话框 -->
    <el-dialog title="批量审核" :visible.sync="batchAuditOpen" width="500px" append-to-body>
      <el-form ref="batchAuditForm" :model="batchAuditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核状态" prop="auditStatus">
          <el-radio-group v-model="batchAuditForm.auditStatus">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="auditRemark">
          <el-input v-model="batchAuditForm.auditRemark" type="textarea" placeholder="请输入审核备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBatchAudit">确 定</el-button>
        <el-button @click="cancelBatchAudit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPosition, getPosition, delPosition, addPosition, updatePosition, auditPosition, batchAuditPositions, updatePositionStatus, updatePositionTop, updatePositionUrgent } from "@/api/recruitment/position";

export default {
  name: "Position",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 职位表格数据
      positionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示审核弹出层
      auditOpen: false,
      // 是否显示批量审核弹出层
      batchAuditOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        positionTitle: null,
        companyName: null,
        publishStatus: null,
        auditStatus: null,
        workNature: null,
      },
      // 表单参数
      form: {},
      // 审核表单参数
      auditForm: {},
      // 批量审核表单参数
      batchAuditForm: {},
      // 表单校验
      rules: {
        positionTitle: [
          { required: true, message: "职位标题不能为空", trigger: "blur" }
        ],
        employerId: [
          { required: true, message: "商家ID不能为空", trigger: "blur" }
        ],
      },
      // 审核表单校验
      auditRules: {
        auditStatus: [
          { required: true, message: "审核状态不能为空", trigger: "change" }
        ],
        auditRemark: [
          { required: true, message: "审核备注不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询职位列表 */
    getList() {
      this.loading = true;
      listPosition(this.queryParams).then(response => {
        this.positionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        positionId: null,
        employerId: null,
        positionTitle: null,
        positionType: null,
        industry: null,
        workNature: null,
        salaryMin: null,
        salaryMax: null,
        salaryUnit: "月",
        workExperience: null,
        education: null,
        workAddress: null,
        jobDescription: null,
        jobRequirements: null,
        welfareBenefits: null,
        contactPerson: null,
        contactPhone: null,
        contactWechat: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.positionId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加职位";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const positionId = row.positionId || this.ids
      getPosition(positionId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改职位";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const positionId = row.positionId || this.ids
      getPosition(positionId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "查看职位详情";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.positionId != null) {
            updatePosition(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPosition(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const positionIds = row.positionId || this.ids;
      this.$modal.confirm('是否确认删除职位编号为"' + positionIds + '"的数据项？').then(function() {
        return delPosition(positionIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('recruitment/position/export', {
        ...this.queryParams
      }, `position_${new Date().getTime()}.xlsx`)
    },
    /** 批量审核按钮操作 */
    handleBatchAudit() {
      this.batchAuditForm = {
        auditStatus: null,
        auditRemark: null
      };
      this.batchAuditOpen = true;
    },
    /** 提交批量审核 */
    submitBatchAudit() {
      this.$refs["batchAuditForm"].validate(valid => {
        if (valid) {
          batchAuditPositions(this.ids, this.batchAuditForm.auditStatus, this.batchAuditForm.auditRemark).then(response => {
            this.$modal.msgSuccess("批量审核成功");
            this.batchAuditOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消批量审核 */
    cancelBatchAudit() {
      this.batchAuditOpen = false;
      this.batchAuditForm = {};
    },
    /** 更多操作 */
    handleCommand(command, row) {
      switch (command) {
        case "handleAudit":
          this.handleAudit(row);
          break;
        case "handleStatus":
          this.handleStatus(row);
          break;
        case "handleTop":
          this.handleTop(row);
          break;
        case "handleUrgent":
          this.handleUrgent(row);
          break;
        default:
          break;
      }
    },
    /** 审核操作 */
    handleAudit(row) {
      this.auditForm = {
        positionId: row.positionId,
        auditStatus: null,
        auditRemark: null
      };
      this.auditOpen = true;
    },
    /** 提交审核 */
    submitAudit() {
      this.$refs["auditForm"].validate(valid => {
        if (valid) {
          auditPosition(this.auditForm).then(response => {
            this.$modal.msgSuccess("审核成功");
            this.auditOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消审核 */
    cancelAudit() {
      this.auditOpen = false;
      this.auditForm = {};
    },
    /** 状态操作 */
    handleStatus(row) {
      let text = row.publishStatus === 2 ? "下线" : "上线";
      let status = row.publishStatus === 2 ? 3 : 2;
      this.$modal.confirm('确认要"' + text + '""' + row.positionTitle + '"职位吗？').then(function() {
        return updatePositionStatus(row.positionId, status);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {});
    },
    /** 置顶操作 */
    handleTop(row) {
      let text = row.isTop === 1 ? "取消置顶" : "置顶";
      let isTop = row.isTop === 1 ? 0 : 1;
      this.$modal.confirm('确认要"' + text + '""' + row.positionTitle + '"职位吗？').then(function() {
        return updatePositionTop(row.positionId, isTop);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {});
    },
    /** 紧急操作 */
    handleUrgent(row) {
      let text = row.isUrgent === 1 ? "取消紧急" : "设为紧急";
      let isUrgent = row.isUrgent === 1 ? 0 : 1;
      this.$modal.confirm('确认要"' + text + '""' + row.positionTitle + '"职位吗？').then(function() {
        return updatePositionUrgent(row.positionId, isUrgent);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {});
    }
  }
};
</script>
