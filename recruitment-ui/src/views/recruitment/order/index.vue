<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="求职者" prop="seekerNickname">
        <el-input
          v-model="queryParams.seekerNickname"
          placeholder="请输入求职者昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单类型" prop="orderType">
        <el-select v-model="queryParams.orderType" placeholder="请选择订单类型" clearable>
          <el-option label="单次查看" value="1" />
          <el-option label="会员套餐" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态" prop="orderStatus">
        <el-select v-model="queryParams.orderStatus" placeholder="请选择订单状态" clearable>
          <el-option label="待支付" value="0" />
          <el-option label="已支付" value="1" />
          <el-option label="已取消" value="2" />
          <el-option label="已退款" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="支付状态" prop="payStatus">
        <el-select v-model="queryParams.payStatus" placeholder="请选择支付状态" clearable>
          <el-option label="未支付" value="0" />
          <el-option label="已支付" value="1" />
          <el-option label="支付失败" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['recruitment:order:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['recruitment:order:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单ID" align="center" prop="orderId" width="80" />
      <el-table-column label="订单号" align="center" prop="orderNo" :show-overflow-tooltip="true" />
      <el-table-column label="求职者" align="center" prop="seekerNickname" />
      <el-table-column label="套餐名称" align="center" prop="packageName" :show-overflow-tooltip="true" />
      <el-table-column label="订单类型" align="center" prop="orderType" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.orderType == 1" type="info">单次查看</el-tag>
          <el-tag v-else-if="scope.row.orderType == 2" type="success">会员套餐</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="订单金额" align="center" prop="orderAmount" width="100">
        <template slot-scope="scope">
          <span style="color: #E6A23C;">¥{{ scope.row.orderAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实付金额" align="center" prop="payAmount" width="100">
        <template slot-scope="scope">
          <span style="color: #F56C6C;">¥{{ scope.row.payAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" prop="orderStatus" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.orderStatus == 0" type="warning">待支付</el-tag>
          <el-tag v-else-if="scope.row.orderStatus == 1" type="success">已支付</el-tag>
          <el-tag v-else-if="scope.row.orderStatus == 2" type="info">已取消</el-tag>
          <el-tag v-else-if="scope.row.orderStatus == 3" type="danger">已退款</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center" prop="payStatus" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.payStatus == 0" type="warning">未支付</el-tag>
          <el-tag v-else-if="scope.row.payStatus == 1" type="success">已支付</el-tag>
          <el-tag v-else-if="scope.row.payStatus == 2" type="danger">支付失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="支付时间" align="center" prop="payTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.payTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['recruitment:order:query']"
          >查看</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['recruitment:order:edit']">
            <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="handleCancel" icon="el-icon-close" v-if="scope.row.orderStatus == 0">取消订单</el-dropdown-item>
              <el-dropdown-item command="handleRefund" icon="el-icon-refresh-left" v-if="scope.row.orderStatus == 1">申请退款</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="open" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单号">{{ form.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="求职者">{{ form.seekerNickname }}</el-descriptions-item>
        <el-descriptions-item label="套餐名称">{{ form.packageName }}</el-descriptions-item>
        <el-descriptions-item label="订单类型">
          <el-tag v-if="form.orderType == 1" type="info">单次查看</el-tag>
          <el-tag v-else-if="form.orderType == 2" type="success">会员套餐</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="订单金额">
          <span style="color: #E6A23C;">¥{{ form.orderAmount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="实付金额">
          <span style="color: #F56C6C;">¥{{ form.payAmount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag v-if="form.orderStatus == 0" type="warning">待支付</el-tag>
          <el-tag v-else-if="form.orderStatus == 1" type="success">已支付</el-tag>
          <el-tag v-else-if="form.orderStatus == 2" type="info">已取消</el-tag>
          <el-tag v-else-if="form.orderStatus == 3" type="danger">已退款</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="支付状态">
          <el-tag v-if="form.payStatus == 0" type="warning">未支付</el-tag>
          <el-tag v-else-if="form.payStatus == 1" type="success">已支付</el-tag>
          <el-tag v-else-if="form.payStatus == 2" type="danger">支付失败</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="支付方式">{{ form.payType }}</el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ parseTime(form.payTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="微信交易号">{{ form.transactionId }}</el-descriptions-item>
        <el-descriptions-item label="获得次数">{{ form.viewCount }}</el-descriptions-item>
        <el-descriptions-item label="过期时间">{{ parseTime(form.expireTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(form.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ form.remark }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOrder, getOrder, delOrder, cancelOrder, refundOrder } from "@/api/recruitment/order";

export default {
  name: "Order",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单表格数据
      orderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 创建时间时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: null,
        seekerNickname: null,
        orderType: null,
        orderStatus: null,
        payStatus: null,
      },
      // 表单参数
      form: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询订单列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      listOrder(this.queryParams).then(response => {
        this.orderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.orderId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const orderId = row.orderId || this.ids
      getOrder(orderId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "查看订单详情";
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const orderIds = row.orderId || this.ids;
      this.$modal.confirm('是否确认删除订单编号为"' + orderIds + '"的数据项？').then(function() {
        return delOrder(orderIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('recruitment/order/export', {
        ...this.queryParams
      }, `order_${new Date().getTime()}.xlsx`)
    },
    /** 更多操作 */
    handleCommand(command, row) {
      switch (command) {
        case "handleCancel":
          this.handleCancel(row);
          break;
        case "handleRefund":
          this.handleRefund(row);
          break;
        default:
          break;
      }
    },
    /** 取消订单 */
    handleCancel(row) {
      this.$modal.confirm('确认要取消订单"' + row.orderNo + '"吗？').then(function() {
        return cancelOrder(row.orderId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("取消成功");
      }).catch(() => {});
    },
    /** 申请退款 */
    handleRefund(row) {
      this.$prompt('请输入退款原因', "申请退款", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /\S/,
        inputErrorMessage: "退款原因不能为空"
      }).then(({ value }) => {
        refundOrder(row.orderId, value).then(response => {
          this.$modal.msgSuccess("退款申请提交成功");
          this.getList();
        });
      }).catch(() => {});
    }
  }
};
</script>
