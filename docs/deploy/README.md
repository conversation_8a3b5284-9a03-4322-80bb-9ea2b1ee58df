# 招聘平台部署指南

本文档详细介绍如何在生产环境中部署招聘平台系统。

## 服务器环境要求

### 硬件要求
- **CPU**：4核心以上
- **内存**：8GB以上
- **存储**：100GB以上SSD
- **网络**：10Mbps以上带宽

### 软件要求
- **操作系统**：CentOS 7+ / Ubuntu 18.04+
- **Java**：OpenJDK 1.8+
- **MySQL**：8.0+
- **Redis**：5.0+
- **Nginx**：1.18+
- **Node.js**：14+

## 环境准备

### 1. 安装Java环境
```bash
# CentOS
sudo yum install java-1.8.0-openjdk java-1.8.0-openjdk-devel

# Ubuntu
sudo apt update
sudo apt install openjdk-8-jdk

# 验证安装
java -version
```

### 2. 安装MySQL
```bash
# CentOS 7
wget https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm
sudo rpm -ivh mysql80-community-release-el7-3.noarch.rpm
sudo yum install mysql-server

# Ubuntu
sudo apt update
sudo apt install mysql-server

# 启动MySQL服务
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 获取临时密码（CentOS）
sudo grep 'temporary password' /var/log/mysqld.log

# 安全配置
sudo mysql_secure_installation
```

### 3. 安装Redis
```bash
# CentOS
sudo yum install epel-release
sudo yum install redis

# Ubuntu
sudo apt install redis-server

# 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis
```

### 4. 安装Nginx
```bash
# CentOS
sudo yum install nginx

# Ubuntu
sudo apt install nginx

# 启动Nginx服务
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 5. 安装Node.js
```bash
# 使用NodeSource仓库
curl -fsSL https://deb.nodesource.com/setup_14.x | sudo -E bash -
sudo apt-get install -y nodejs

# 或使用nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.38.0/install.sh | bash
source ~/.bashrc
nvm install 14
nvm use 14
```

## 数据库部署

### 1. 创建数据库和用户
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE recruitment_platform DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 创建用户
CREATE USER 'recruitment'@'%' IDENTIFIED BY 'your_strong_password';

-- 授权
GRANT ALL PRIVILEGES ON recruitment_platform.* TO 'recruitment'@'%';
FLUSH PRIVILEGES;
```

### 2. 导入数据库结构和数据
```bash
# 上传SQL文件到服务器
scp sql/init_database.sql user@server:/tmp/

# 执行初始化脚本
mysql -u recruitment -p recruitment_platform < /tmp/init_database.sql
```

### 3. 配置MySQL
```bash
# 编辑MySQL配置文件
sudo vim /etc/mysql/mysql.conf.d/mysqld.cnf

# 添加以下配置
[mysqld]
max_connections = 1000
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 重启MySQL
sudo systemctl restart mysql
```

## 后端部署

### 1. 编译打包
```bash
# 在开发机器上编译
cd recruitment-api
mvn clean package -Dmaven.test.skip=true

# 打包后的文件位于
ls ruoyi-admin/target/ruoyi-admin.jar
```

### 2. 上传到服务器
```bash
# 创建应用目录
sudo mkdir -p /opt/recruitment-platform
sudo chown $USER:$USER /opt/recruitment-platform

# 上传jar包
scp recruitment-api/ruoyi-admin/target/ruoyi-admin.jar user@server:/opt/recruitment-platform/

# 上传配置文件
scp -r recruitment-api/ruoyi-admin/src/main/resources/ user@server:/opt/recruitment-platform/config/
```

### 3. 修改生产配置
```bash
# 编辑数据库配置
vim /opt/recruitment-platform/config/application-druid.yml

# 修改数据库连接信息
spring:
  datasource:
    druid:
      master:
        url: *************************************************************************************************************************************************************
        username: recruitment
        password: your_strong_password

# 编辑Redis配置
vim /opt/recruitment-platform/config/application.yml

# 修改Redis连接信息
spring:
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
    database: 0
```

### 4. 创建启动脚本
```bash
# 创建启动脚本
vim /opt/recruitment-platform/start.sh

#!/bin/bash
APP_NAME="ruoyi-admin.jar"
APP_PATH="/opt/recruitment-platform"
CONFIG_PATH="/opt/recruitment-platform/config"

# 检查应用是否运行
PID=$(ps -ef | grep $APP_NAME | grep -v grep | awk '{print $2}')
if [ -n "$PID" ]; then
    echo "Application is already running with PID: $PID"
    exit 1
fi

# 启动应用
nohup java -jar $APP_PATH/$APP_NAME \
    --spring.config.location=$CONFIG_PATH/ \
    --server.port=8080 \
    --spring.profiles.active=prod \
    > $APP_PATH/app.log 2>&1 &

echo "Application started successfully"

# 赋予执行权限
chmod +x /opt/recruitment-platform/start.sh
```

### 5. 创建停止脚本
```bash
# 创建停止脚本
vim /opt/recruitment-platform/stop.sh

#!/bin/bash
APP_NAME="ruoyi-admin.jar"

PID=$(ps -ef | grep $APP_NAME | grep -v grep | awk '{print $2}')
if [ -n "$PID" ]; then
    kill -9 $PID
    echo "Application stopped successfully"
else
    echo "Application is not running"
fi

# 赋予执行权限
chmod +x /opt/recruitment-platform/stop.sh
```

### 6. 创建系统服务
```bash
# 创建systemd服务文件
sudo vim /etc/systemd/system/recruitment-platform.service

[Unit]
Description=Recruitment Platform Application
After=network.target

[Service]
Type=forking
User=recruitment
Group=recruitment
WorkingDirectory=/opt/recruitment-platform
ExecStart=/opt/recruitment-platform/start.sh
ExecStop=/opt/recruitment-platform/stop.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target

# 重载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable recruitment-platform

# 启动服务
sudo systemctl start recruitment-platform

# 查看状态
sudo systemctl status recruitment-platform
```

## 前端部署

### 1. 构建前端项目
```bash
# 在开发机器上构建
cd recruitment-ui

# 安装依赖
npm install

# 构建生产版本
npm run build:prod

# 打包构建结果
tar -czf dist.tar.gz dist/
```

### 2. 部署到Nginx
```bash
# 上传构建结果
scp dist.tar.gz user@server:/tmp/

# 在服务器上解压
cd /var/www/html
sudo tar -xzf /tmp/dist.tar.gz
sudo mv dist recruitment-platform
sudo chown -R www-data:www-data recruitment-platform
```

### 3. 配置Nginx
```bash
# 创建Nginx配置文件
sudo vim /etc/nginx/sites-available/recruitment-platform

server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /var/www/html/recruitment-platform;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /prod-api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# 启用站点
sudo ln -s /etc/nginx/sites-available/recruitment-platform /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载Nginx
sudo systemctl reload nginx
```

## SSL证书配置

### 1. 使用Let's Encrypt
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. 更新Nginx配置
```bash
# Certbot会自动更新配置，或手动添加SSL配置
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 其他配置...
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## 监控和日志

### 1. 应用日志
```bash
# 查看应用日志
tail -f /opt/recruitment-platform/app.log

# 配置日志轮转
sudo vim /etc/logrotate.d/recruitment-platform

/opt/recruitment-platform/app.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
```

### 2. 系统监控
```bash
# 安装htop
sudo apt install htop

# 监控系统资源
htop

# 监控磁盘使用
df -h

# 监控网络连接
netstat -tulpn
```

## 备份策略

### 1. 数据库备份
```bash
# 创建备份脚本
vim /opt/backup/mysql_backup.sh

#!/bin/bash
BACKUP_DIR="/opt/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="recruitment_platform"
DB_USER="recruitment"
DB_PASS="your_password"

mkdir -p $BACKUP_DIR

mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "backup_*.sql" -mtime +7 -delete

# 添加到crontab
crontab -e
# 每天凌晨2点备份
0 2 * * * /opt/backup/mysql_backup.sh
```

### 2. 文件备份
```bash
# 备份应用文件
tar -czf /opt/backup/app_$(date +%Y%m%d).tar.gz /opt/recruitment-platform

# 备份前端文件
tar -czf /opt/backup/web_$(date +%Y%m%d).tar.gz /var/www/html/recruitment-platform
```

## 故障排查

### 1. 常见问题
- **应用无法启动**：检查Java版本、配置文件、端口占用
- **数据库连接失败**：检查数据库服务状态、用户权限、网络连接
- **前端页面无法访问**：检查Nginx配置、文件权限、代理设置

### 2. 日志查看
```bash
# 应用日志
tail -f /opt/recruitment-platform/app.log

# Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# 系统日志
journalctl -u recruitment-platform -f
```

## 性能优化

### 1. JVM优化
```bash
# 修改启动脚本，添加JVM参数
java -jar -Xms2g -Xmx4g -XX:+UseG1GC \
    -XX:MaxGCPauseMillis=200 \
    -XX:+HeapDumpOnOutOfMemoryError \
    ruoyi-admin.jar
```

### 2. 数据库优化
```sql
-- 添加必要的索引
-- 定期分析表
ANALYZE TABLE rec_job_position;
ANALYZE TABLE rec_job_seeker;
ANALYZE TABLE rec_order;
```

### 3. Redis优化
```bash
# 编辑Redis配置
vim /etc/redis/redis.conf

# 设置最大内存
maxmemory 1gb
maxmemory-policy allkeys-lru

# 启用持久化
save 900 1
save 300 10
save 60 10000
```

这样就完成了完整的部署指南文档。接下来我会创建开发指南文档来完成项目文档的创建。
