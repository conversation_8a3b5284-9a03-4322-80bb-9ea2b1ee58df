# 招聘平台开发指南

本文档为开发者提供详细的开发指南，包括项目结构、开发规范、调试方法等。

## 开发环境搭建

### 必需软件
- **JDK 1.8+**：推荐使用OpenJDK 8或Oracle JDK 8
- **Maven 3.6+**：用于Java项目构建
- **MySQL 8.0+**：数据库服务
- **Redis 5.0+**：缓存服务
- **Node.js 14+**：前端开发环境
- **Git**：版本控制

### 开发工具推荐
- **后端IDE**：IntelliJ IDEA / Eclipse
- **前端IDE**：VS Code / WebStorm
- **小程序IDE**：HBuilderX / 微信开发者工具
- **数据库工具**：Navicat / DBeaver
- **API测试**：Postman / Apifox

## 项目结构详解

### 后端模块结构
```
recruitment-api/
├── ruoyi-admin/                    # 启动模块
│   ├── src/main/java/
│   │   └── com/ruoyi/
│   │       ├── RuoYiApplication.java    # 启动类
│   │       └── web/controller/          # 控制器
│   └── src/main/resources/
│       ├── application.yml              # 主配置文件
│       ├── application-druid.yml        # 数据源配置
│       └── logback.xml                  # 日志配置
├── ruoyi-common/                   # 通用模块
│   └── src/main/java/com/ruoyi/common/
│       ├── annotation/                  # 自定义注解
│       ├── config/                      # 配置类
│       ├── core/                        # 核心类
│       ├── enums/                       # 枚举类
│       ├── exception/                   # 异常类
│       └── utils/                       # 工具类
├── ruoyi-framework/                # 框架模块
│   └── src/main/java/com/ruoyi/framework/
│       ├── config/                      # 框架配置
│       ├── interceptor/                 # 拦截器
│       ├── security/                    # 安全配置
│       └── web/                         # Web配置
├── ruoyi-system/                   # 系统模块
│   └── src/main/java/com/ruoyi/system/
│       ├── domain/                      # 实体类
│       ├── mapper/                      # 数据访问层
│       └── service/                     # 业务逻辑层
└── ruoyi-recruitment/              # 招聘业务模块
    └── src/main/java/com/ruoyi/recruitment/
        ├── controller/                  # 控制器
        ├── domain/                      # 实体类
        ├── mapper/                      # 数据访问层
        ├── service/                     # 业务逻辑层
        ├── enums/                       # 枚举类
        └── constants/                   # 常量类
```

### 前端项目结构
```
recruitment-ui/
├── public/                         # 静态资源
├── src/
│   ├── api/                        # API接口定义
│   │   └── recruitment/            # 招聘相关API
│   ├── assets/                     # 资源文件
│   │   ├── icons/                  # 图标
│   │   ├── images/                 # 图片
│   │   └── styles/                 # 样式文件
│   ├── components/                 # 公共组件
│   ├── layout/                     # 布局组件
│   ├── router/                     # 路由配置
│   ├── store/                      # Vuex状态管理
│   ├── utils/                      # 工具函数
│   └── views/                      # 页面组件
│       └── recruitment/            # 招聘相关页面
├── package.json                    # 依赖配置
└── vue.config.js                   # Vue配置
```

## 开发规范

### 后端开发规范

#### 1. 命名规范
```java
// 类名：大驼峰命名法
public class RecJobSeekerController {}

// 方法名：小驼峰命名法
public AjaxResult selectRecJobSeekerList() {}

// 常量：全大写，下划线分隔
public static final String ORDER_NO_PREFIX = "REC";

// 变量名：小驼峰命名法
private Long seekerId;
```

#### 2. 注释规范
```java
/**
 * 求职者管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/recruitment/seeker")
public class RecJobSeekerController {
    
    /**
     * 查询求职者列表
     * 
     * @param recJobSeeker 查询条件
     * @return 求职者列表
     */
    @GetMapping("/list")
    public TableDataInfo list(RecJobSeeker recJobSeeker) {
        // 方法实现
    }
}
```

#### 3. 异常处理
```java
// 使用自定义异常
throw new ServiceException("用户不存在");

// 统一异常处理
@ControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(ServiceException.class)
    public AjaxResult handleServiceException(ServiceException e) {
        return AjaxResult.error(e.getMessage());
    }
}
```

#### 4. 日志规范
```java
// 使用slf4j日志
private static final Logger log = LoggerFactory.getLogger(RecJobSeekerServiceImpl.class);

// 日志级别使用
log.debug("调试信息：{}", debugInfo);
log.info("业务信息：{}", businessInfo);
log.warn("警告信息：{}", warnInfo);
log.error("错误信息：{}", errorInfo, exception);
```

### 前端开发规范

#### 1. 组件命名
```javascript
// 组件文件名：大驼峰命名法
RecJobSeekerList.vue

// 组件注册名：短横线命名法
export default {
  name: 'RecJobSeekerList'
}
```

#### 2. API调用规范
```javascript
// API文件组织
// src/api/recruitment/seeker.js
import request from '@/utils/request'

export function listSeeker(query) {
  return request({
    url: '/recruitment/seeker/list',
    method: 'get',
    params: query
  })
}

// 组件中使用
import { listSeeker } from '@/api/recruitment/seeker'

export default {
  methods: {
    async getList() {
      try {
        const response = await listSeeker(this.queryParams)
        this.seekerList = response.rows
        this.total = response.total
      } catch (error) {
        this.$modal.msgError('获取数据失败')
      }
    }
  }
}
```

#### 3. 样式规范
```vue
<template>
  <div class="app-container">
    <el-form class="search-form">
      <!-- 表单内容 -->
    </el-form>
  </div>
</template>

<style scoped>
.app-container {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
}
</style>
```

## 数据库设计规范

### 1. 表命名规范
- 表名使用小写字母和下划线
- 统一使用`rec_`前缀
- 表名要有明确的业务含义

### 2. 字段命名规范
```sql
-- 主键统一命名为：表名_id
CREATE TABLE rec_job_seeker (
  seeker_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '求职者ID',
  -- 其他字段
);

-- 时间字段统一后缀
create_time datetime COMMENT '创建时间',
update_time datetime COMMENT '更新时间',

-- 状态字段统一命名
status char(1) DEFAULT '0' COMMENT '状态（0正常 1禁用）',
```

### 3. 索引设计
```sql
-- 主键索引
PRIMARY KEY (seeker_id),

-- 唯一索引
UNIQUE KEY uk_openid (openid),

-- 普通索引
KEY idx_region_code (region_code),

-- 复合索引
KEY idx_status_create_time (status, create_time),
```

## 调试指南

### 后端调试

#### 1. IDEA调试配置
```
1. 创建Spring Boot运行配置
2. 设置主类：com.ruoyi.RuoYiApplication
3. 设置VM参数：-Dspring.profiles.active=dev
4. 设置程序参数：--server.port=8080
```

#### 2. 日志调试
```yaml
# application-dev.yml
logging:
  level:
    com.ruoyi.recruitment: debug
    org.springframework.web: debug
    org.mybatis: debug
```

#### 3. SQL调试
```yaml
# MyBatis SQL日志
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
```

### 前端调试

#### 1. 开发服务器配置
```javascript
// vue.config.js
module.exports = {
  devServer: {
    port: 80,
    open: true,
    proxy: {
      '/dev-api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        pathRewrite: {
          '^/dev-api': ''
        }
      }
    }
  }
}
```

#### 2. 浏览器调试
- 使用Chrome DevTools
- 安装Vue DevTools扩展
- 使用console.log()输出调试信息

## 测试指南

### 单元测试
```java
@SpringBootTest
class RecJobSeekerServiceTest {
    
    @Autowired
    private IRecJobSeekerService recJobSeekerService;
    
    @Test
    void testSelectRecJobSeekerList() {
        RecJobSeeker seeker = new RecJobSeeker();
        List<RecJobSeeker> list = recJobSeekerService.selectRecJobSeekerList(seeker);
        assertNotNull(list);
    }
}
```

### API测试
使用Postman或Apifox进行API测试：
```
GET /recruitment/seeker/list
Authorization: Bearer {token}
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "nickname": "测试"
}
```

## 常见问题

### 1. 跨域问题
```java
// 后端配置CORS
@Configuration
public class CorsConfig {
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        config.addAllowedOriginPattern("*");
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }
}
```

### 2. 数据库连接问题
```yaml
# 检查数据库配置
spring:
  datasource:
    druid:
      master:
        url: *************************************************************************************************************************************************************
        username: root
        password: password
```

### 3. 权限问题
```java
// 检查权限注解
@PreAuthorize("@ss.hasPermi('recruitment:seeker:list')")
@GetMapping("/list")
public TableDataInfo list(RecJobSeeker recJobSeeker) {
    // 方法实现
}
```

## 代码生成

### 使用若依代码生成器
1. 访问：http://localhost:8080/tool/gen
2. 导入数据库表
3. 配置生成信息
4. 生成代码并下载
5. 将代码复制到对应模块

### 自定义模板
可以修改`ruoyi-generator`模块中的模板文件来自定义生成的代码格式。

## 版本控制

### Git工作流
```bash
# 创建功能分支
git checkout -b feature/seeker-management

# 提交代码
git add .
git commit -m "feat: 添加求职者管理功能"

# 推送分支
git push origin feature/seeker-management

# 创建Pull Request
```

### 提交信息规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 性能优化

### 后端优化
1. **数据库优化**：添加索引、优化SQL查询
2. **缓存优化**：使用Redis缓存热点数据
3. **分页优化**：使用PageHelper进行分页查询
4. **异步处理**：使用@Async处理耗时操作

### 前端优化
1. **路由懒加载**：使用动态import
2. **组件懒加载**：按需加载组件
3. **图片优化**：使用WebP格式、图片压缩
4. **打包优化**：代码分割、Tree Shaking

这样就完成了开发指南文档的创建。现在让我完成最后一个任务：
