-- 招聘平台数据库初始化脚本
-- 创建时间：2025-01-01
-- 说明：此脚本用于初始化招聘平台数据库，包括创建数据库、表结构和初始数据

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `recruitment_platform` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE `recruitment_platform`;

-- 设置SQL模式
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- 执行表结构创建
SOURCE recruitment_tables.sql;

-- 执行初始数据插入
SOURCE recruitment_data.sql;

-- 创建索引优化
-- 为常用查询字段添加复合索引
ALTER TABLE `rec_job_position` ADD INDEX `idx_publish_audit_status` (`publish_status`, `audit_status`);
ALTER TABLE `rec_job_position` ADD INDEX `idx_region_publish_time` (`region_code`, `publish_time`);
ALTER TABLE `rec_job_position` ADD INDEX `idx_employer_publish_status` (`employer_id`, `publish_status`);

ALTER TABLE `rec_view_record` ADD INDEX `idx_seeker_view_time` (`seeker_id`, `view_time`);
ALTER TABLE `rec_view_record` ADD INDEX `idx_position_view_time` (`position_id`, `view_time`);

ALTER TABLE `rec_order` ADD INDEX `idx_seeker_create_time` (`seeker_id`, `create_time`);
ALTER TABLE `rec_order` ADD INDEX `idx_status_create_time` (`order_status`, `create_time`);

-- 添加外键约束
ALTER TABLE `rec_job_position` ADD CONSTRAINT `fk_position_employer` FOREIGN KEY (`employer_id`) REFERENCES `rec_employer` (`employer_id`) ON DELETE CASCADE;
ALTER TABLE `rec_order` ADD CONSTRAINT `fk_order_seeker` FOREIGN KEY (`seeker_id`) REFERENCES `rec_job_seeker` (`seeker_id`) ON DELETE CASCADE;
ALTER TABLE `rec_order` ADD CONSTRAINT `fk_order_package` FOREIGN KEY (`package_id`) REFERENCES `rec_member_package` (`package_id`) ON DELETE SET NULL;
ALTER TABLE `rec_view_record` ADD CONSTRAINT `fk_record_seeker` FOREIGN KEY (`seeker_id`) REFERENCES `rec_job_seeker` (`seeker_id`) ON DELETE CASCADE;
ALTER TABLE `rec_view_record` ADD CONSTRAINT `fk_record_position` FOREIGN KEY (`position_id`) REFERENCES `rec_job_position` (`position_id`) ON DELETE CASCADE;
ALTER TABLE `rec_view_record` ADD CONSTRAINT `fk_record_employer` FOREIGN KEY (`employer_id`) REFERENCES `rec_employer` (`employer_id`) ON DELETE CASCADE;

-- 创建视图
-- 职位详情视图（包含商家信息）
CREATE VIEW `v_position_detail` AS
SELECT 
    p.*,
    e.company_name,
    e.company_scale,
    e.company_nature,
    e.company_description,
    e.logo_url,
    r.region_name
FROM `rec_job_position` p
LEFT JOIN `rec_employer` e ON p.employer_id = e.employer_id
LEFT JOIN `rec_region` r ON p.region_code = r.region_code;

-- 求职者统计视图
CREATE VIEW `v_seeker_stats` AS
SELECT 
    s.*,
    r.region_name,
    COALESCE(order_stats.total_orders, 0) as total_orders,
    COALESCE(order_stats.total_amount, 0) as total_paid_amount,
    COALESCE(view_stats.total_views, 0) as total_views
FROM `rec_job_seeker` s
LEFT JOIN `rec_region` r ON s.region_code = r.region_code
LEFT JOIN (
    SELECT 
        seeker_id,
        COUNT(*) as total_orders,
        SUM(pay_amount) as total_amount
    FROM `rec_order` 
    WHERE order_status = 1 
    GROUP BY seeker_id
) order_stats ON s.seeker_id = order_stats.seeker_id
LEFT JOIN (
    SELECT 
        seeker_id,
        COUNT(*) as total_views
    FROM `rec_view_record` 
    GROUP BY seeker_id
) view_stats ON s.seeker_id = view_stats.seeker_id;

-- 商家统计视图
CREATE VIEW `v_employer_stats` AS
SELECT 
    e.*,
    r.region_name,
    COALESCE(pos_stats.total_positions, 0) as total_positions,
    COALESCE(pos_stats.published_positions, 0) as published_positions,
    COALESCE(view_stats.total_views, 0) as total_views
FROM `rec_employer` e
LEFT JOIN `rec_region` r ON e.region_code = r.region_code
LEFT JOIN (
    SELECT 
        employer_id,
        COUNT(*) as total_positions,
        SUM(CASE WHEN publish_status = 2 THEN 1 ELSE 0 END) as published_positions
    FROM `rec_job_position` 
    GROUP BY employer_id
) pos_stats ON e.employer_id = pos_stats.employer_id
LEFT JOIN (
    SELECT 
        employer_id,
        COUNT(*) as total_views
    FROM `rec_view_record` 
    GROUP BY employer_id
) view_stats ON e.employer_id = view_stats.employer_id;

-- 创建存储过程
-- 自动取消过期订单的存储过程
DELIMITER $$
CREATE PROCEDURE `sp_cancel_expired_orders`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE order_id_var BIGINT;
    DECLARE cur CURSOR FOR 
        SELECT order_id FROM rec_order 
        WHERE order_status = 0 AND expire_time < NOW();
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO order_id_var;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        UPDATE rec_order 
        SET order_status = 2, update_time = NOW() 
        WHERE order_id = order_id_var;
    END LOOP;
    CLOSE cur;
    
    SELECT ROW_COUNT() as cancelled_orders;
END$$
DELIMITER ;

-- 创建触发器
-- 职位浏览次数更新触发器
DELIMITER $$
CREATE TRIGGER `tr_update_position_view_count` 
AFTER INSERT ON `rec_view_record`
FOR EACH ROW
BEGIN
    UPDATE `rec_job_position` 
    SET view_count = view_count + 1 
    WHERE position_id = NEW.position_id;
END$$
DELIMITER ;

-- 订单支付成功后更新求职者信息触发器
DELIMITER $$
CREATE TRIGGER `tr_update_seeker_after_payment` 
AFTER UPDATE ON `rec_order`
FOR EACH ROW
BEGIN
    IF NEW.pay_status = 1 AND OLD.pay_status != 1 THEN
        -- 更新求职者的查看次数和消费金额
        UPDATE `rec_job_seeker` 
        SET 
            view_count = view_count + NEW.view_count,
            total_consumed = total_consumed + NEW.pay_amount,
            update_time = NOW()
        WHERE seeker_id = NEW.seeker_id;
        
        -- 如果是会员套餐，更新会员信息
        IF NEW.order_type = 2 THEN
            UPDATE `rec_job_seeker` s
            INNER JOIN `rec_member_package` p ON NEW.package_id = p.package_id
            SET 
                s.member_type = CASE 
                    WHEN p.price >= 100 THEN 3
                    WHEN p.price >= 50 THEN 2
                    ELSE 1
                END,
                s.member_expire_time = CASE 
                    WHEN p.validity_days > 0 THEN DATE_ADD(NOW(), INTERVAL p.validity_days DAY)
                    ELSE NULL
                END,
                s.update_time = NOW()
            WHERE s.seeker_id = NEW.seeker_id;
        END IF;
    END IF;
END$$
DELIMITER ;

-- 提交事务
COMMIT;

-- 显示初始化完成信息
SELECT 'Database initialization completed successfully!' as message;
SELECT COUNT(*) as total_tables FROM information_schema.tables WHERE table_schema = 'recruitment_platform';
SELECT COUNT(*) as total_views FROM information_schema.views WHERE table_schema = 'recruitment_platform';
SELECT COUNT(*) as total_procedures FROM information_schema.routines WHERE routine_schema = 'recruitment_platform' AND routine_type = 'PROCEDURE';
SELECT COUNT(*) as total_triggers FROM information_schema.triggers WHERE trigger_schema = 'recruitment_platform';
