-- 招聘平台初始数据
-- 创建时间：2025-01-01

-- 1. 系统配置数据
INSERT INTO `rec_system_config` VALUES 
(1, 'wechat.appid', 'your_wechat_appid', 'string', '微信小程序AppID', 1, 'admin', NOW(), 'admin', NOW()),
(2, 'wechat.secret', 'your_wechat_secret', 'string', '微信小程序Secret', 1, 'admin', NOW(), 'admin', NOW()),
(3, 'wechat.mch_id', 'your_mch_id', 'string', '微信商户号', 1, 'admin', NOW(), 'admin', NOW()),
(4, 'wechat.api_key', 'your_api_key', 'string', '微信支付API密钥', 1, 'admin', NOW(), 'admin', NOW()),
(5, 'single_view_price', '5.00', 'decimal', '单次查看价格', 1, 'admin', NOW(), 'admin', NOW()),
(6, 'platform_name', '招聘平台', 'string', '平台名称', 1, 'admin', NOW(), 'admin', NOW()),
(7, 'contact_phone', '************', 'string', '客服电话', 1, 'admin', NOW(), 'admin', NOW()),
(8, 'contact_email', '<EMAIL>', 'string', '客服邮箱', 1, 'admin', NOW(), 'admin', NOW());

-- 2. 会员套餐数据
INSERT INTO `rec_member_package` VALUES 
(1, '单次查看', 1, 5.00, 1, 0, '单次查看职位联系方式', 1, 1, 'admin', NOW(), 'admin', NOW()),
(2, '30元会员套餐', 2, 30.00, 20, 30, '30天内可查看20次职位联系方式', 1, 2, 'admin', NOW(), 'admin', NOW()),
(3, '50元会员套餐', 2, 50.00, 50, 30, '30天内可查看50次职位联系方式', 1, 3, 'admin', NOW(), 'admin', NOW()),
(4, '100元会员套餐', 2, 100.00, 0, 90, '90天内无限次查看职位联系方式', 1, 4, 'admin', NOW(), 'admin', NOW());

-- 3. 地区数据（部分省市数据）
INSERT INTO `rec_region` VALUES 
-- 省级
(1, '110000', '北京市', NULL, 1, 1, 1, 'admin', NOW(), 'admin', NOW()),
(2, '120000', '天津市', NULL, 1, 2, 1, 'admin', NOW(), 'admin', NOW()),
(3, '310000', '上海市', NULL, 1, 3, 1, 'admin', NOW(), 'admin', NOW()),
(4, '500000', '重庆市', NULL, 1, 4, 1, 'admin', NOW(), 'admin', NOW()),
(5, '440000', '广东省', NULL, 1, 5, 1, 'admin', NOW(), 'admin', NOW()),
(6, '320000', '江苏省', NULL, 1, 6, 1, 'admin', NOW(), 'admin', NOW()),
(7, '330000', '浙江省', NULL, 1, 7, 1, 'admin', NOW(), 'admin', NOW()),
(8, '370000', '山东省', NULL, 1, 8, 1, 'admin', NOW(), 'admin', NOW()),

-- 市级（以广东省为例）
(9, '440100', '广州市', '440000', 2, 1, 1, 'admin', NOW(), 'admin', NOW()),
(10, '440300', '深圳市', '440000', 2, 2, 1, 'admin', NOW(), 'admin', NOW()),
(11, '440400', '珠海市', '440000', 2, 3, 1, 'admin', NOW(), 'admin', NOW()),
(12, '440500', '汕头市', '440000', 2, 4, 1, 'admin', NOW(), 'admin', NOW()),
(13, '440600', '佛山市', '440000', 2, 5, 1, 'admin', NOW(), 'admin', NOW()),
(14, '440700', '江门市', '440000', 2, 6, 1, 'admin', NOW(), 'admin', NOW()),
(15, '440800', '湛江市', '440000', 2, 7, 1, 'admin', NOW(), 'admin', NOW()),
(16, '441200', '肇庆市', '440000', 2, 8, 1, 'admin', NOW(), 'admin', NOW()),

-- 区县级（以广州市为例）
(17, '440103', '荔湾区', '440100', 3, 1, 1, 'admin', NOW(), 'admin', NOW()),
(18, '440104', '越秀区', '440100', 3, 2, 1, 'admin', NOW(), 'admin', NOW()),
(19, '440105', '海珠区', '440100', 3, 3, 1, 'admin', NOW(), 'admin', NOW()),
(20, '440106', '天河区', '440100', 3, 4, 1, 'admin', NOW(), 'admin', NOW()),
(21, '440111', '白云区', '440100', 3, 5, 1, 'admin', NOW(), 'admin', NOW()),
(22, '440112', '黄埔区', '440100', 3, 6, 1, 'admin', NOW(), 'admin', NOW()),
(23, '440113', '番禺区', '440100', 3, 7, 1, 'admin', NOW(), 'admin', NOW()),
(24, '440114', '花都区', '440100', 3, 8, 1, 'admin', NOW(), 'admin', NOW());

-- 4. 示例商家数据
INSERT INTO `rec_employer` VALUES 
(1, '阿里巴巴集团', '张经理', '13800138001', '<EMAIL>', '91330000MA27XK4X0E', NULL, '杭州市余杭区文一西路969号', '330100', '10000人以上', '民营企业', '阿里巴巴集团控股有限公司是以曾担任英语教师的马云为首的18人于1999年在浙江省杭州市创立的公司。', NULL, 1, '审核通过', NOW(), 'admin', '0', 'alibaba', '$2a$10$7JB720yubVSOfvVame6NEOhxzxWtnKxu7RdQSvgdwxAU5N/Sj9ydO', 'admin', NOW(), 'admin', NOW()),
(2, '腾讯科技有限公司', '李经理', '13800138002', '<EMAIL>', '91440300708461136T', NULL, '深圳市南山区科技园', '440300', '5000-10000人', '民营企业', '腾讯成立于1998年11月，是目前中国领先的互联网增值服务提供商之一。', NULL, 1, '审核通过', NOW(), 'admin', '0', 'tencent', '$2a$10$7JB720yubVSOfvVame6NEOhxzxWtnKxu7RdQSvgdwxAU5N/Sj9ydO', 'admin', NOW(), 'admin', NOW()),
(3, '百度在线网络技术有限公司', '王经理', '13800138003', '<EMAIL>', '91110000802100433B', NULL, '北京市海淀区上地十街10号', '110000', '5000-10000人', '民营企业', '百度是拥有强大互联网基础的领先AI公司。', NULL, 1, '审核通过', NOW(), 'admin', '0', 'baidu', '$2a$10$7JB720yubVSOfvVame6NEOhxzxWtnKxu7RdQSvgdwxAU5N/Sj9ydO', 'admin', NOW(), 'admin', NOW());

-- 5. 示例职位数据
INSERT INTO `rec_job_position` VALUES 
(1, 1, 'Java高级开发工程师', '技术开发', 'IT互联网', '全职', 20000.00, 35000.00, '月', '3-5年', '本科', '杭州市余杭区文一西路969号', '330100', '负责公司核心业务系统的开发和维护，参与系统架构设计，解决技术难题。', '1. 计算机相关专业本科以上学历；2. 3年以上Java开发经验；3. 熟悉Spring、MyBatis等框架；4. 有分布式系统开发经验优先。', '五险一金、年终奖、股票期权、带薪年假、免费三餐', '张经理', '13800138001', 'hr_zhang', 2, 1, '职位信息完整，审核通过', NOW(), 'admin', NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 156, 23, 1, 0, 'admin', NOW(), 'admin', NOW()),
(2, 2, '前端开发工程师', '技术开发', 'IT互联网', '全职', 15000.00, 25000.00, '月', '2-3年', '本科', '深圳市南山区科技园', '440300', '负责公司产品的前端开发，与设计师和后端工程师协作完成产品功能。', '1. 计算机相关专业本科以上学历；2. 2年以上前端开发经验；3. 熟悉Vue、React等框架；4. 有移动端开发经验优先。', '五险一金、年终奖、股票期权、弹性工作、健身房', '李经理', '13800138002', 'hr_li', 2, 1, '职位信息完整，审核通过', NOW(), 'admin', NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 89, 12, 0, 1, 'admin', NOW(), 'admin', NOW()),
(3, 3, '产品经理', '产品运营', 'IT互联网', '全职', 18000.00, 30000.00, '月', '3-5年', '本科', '北京市海淀区上地十街10号', '110000', '负责产品规划和设计，与技术团队协作推进产品开发，分析用户需求和市场趋势。', '1. 本科以上学历，计算机或相关专业优先；2. 3年以上产品经验；3. 熟悉互联网产品设计流程；4. 有数据分析能力。', '五险一金、年终奖、股票期权、带薪年假、团建活动', '王经理', '13800138003', 'hr_wang', 2, 1, '职位信息完整，审核通过', NOW(), 'admin', NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 67, 8, 0, 0, 'admin', NOW(), 'admin', NOW());

-- 6. 示例求职者数据
INSERT INTO `rec_job_seeker` VALUES 
(1, 'mock_openid_001', NULL, '13900139001', '小明', 'https://example.com/avatar1.jpg', 1, '440100', 0, NULL, 0, 0.00, '0', NOW(), NOW(), 'system', NOW(), 'system', NOW()),
(2, 'mock_openid_002', NULL, '13900139002', '小红', 'https://example.com/avatar2.jpg', 2, '330100', 1, DATE_ADD(NOW(), INTERVAL 30 DAY), 15, 30.00, '0', NOW(), NOW(), 'system', NOW(), 'system', NOW()),
(3, 'mock_openid_003', NULL, '13900139003', '小李', 'https://example.com/avatar3.jpg', 1, '110000', 2, DATE_ADD(NOW(), INTERVAL 30 DAY), 35, 50.00, '0', NOW(), NOW(), 'system', NOW(), 'system', NOW());

-- 7. 示例订单数据
INSERT INTO `rec_order` VALUES 
(1, 'REC202501010001', 2, 2, 2, 30.00, 30.00, 1, 1, 'wechat_pay', NOW(), 'wx_transaction_001', 20, DATE_ADD(NOW(), INTERVAL 30 MINUTE), '购买30元会员套餐', 'system', NOW(), 'system', NOW()),
(2, 'REC202501010002', 3, 3, 2, 50.00, 50.00, 1, 1, 'wechat_pay', NOW(), 'wx_transaction_002', 50, DATE_ADD(NOW(), INTERVAL 30 MINUTE), '购买50元会员套餐', 'system', NOW(), 'system', NOW());

-- 8. 示例查看记录数据
INSERT INTO `rec_view_record` VALUES 
(1, 2, 1, 1, 2, 0.00, 1, NOW(), 'system', NOW()),
(2, 2, 2, 2, 2, 0.00, 1, NOW(), 'system', NOW()),
(3, 3, 1, 1, 2, 0.00, 2, NOW(), 'system', NOW()),
(4, 3, 3, 3, 2, 0.00, 2, NOW(), 'system', NOW());
