-- 招聘平台数据库表结构
-- 创建时间：2025-01-01

-- 1. 求职者表
DROP TABLE IF EXISTS `rec_job_seeker`;
CREATE TABLE `rec_job_seeker` (
  `seeker_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '求职者ID',
  `openid` varchar(100) DEFAULT NULL COMMENT '微信openid',
  `unionid` varchar(100) DEFAULT NULL COMMENT '微信unionid',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像',
  `gender` tinyint(1) DEFAULT '0' COMMENT '性别（0未知 1男 2女）',
  `region_code` varchar(20) DEFAULT NULL COMMENT '地区编码',
  `member_type` tinyint(1) DEFAULT '0' COMMENT '会员类型（0普通用户 1会员）',
  `member_expire_time` datetime DEFAULT NULL COMMENT '会员到期时间',
  `view_count` int(11) DEFAULT '0' COMMENT '剩余查看次数',
  `total_consumed` decimal(10,2) DEFAULT '0.00' COMMENT '累计消费金额',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1冻结）',
  `register_time` datetime DEFAULT NULL COMMENT '注册时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`seeker_id`),
  UNIQUE KEY `uk_openid` (`openid`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_region_code` (`region_code`),
  KEY `idx_member_type` (`member_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='求职者表';

-- 2. 商家表
DROP TABLE IF EXISTS `rec_employer`;
CREATE TABLE `rec_employer` (
  `employer_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商家ID',
  `company_name` varchar(100) NOT NULL COMMENT '企业名称',
  `contact_person` varchar(50) NOT NULL COMMENT '联系人',
  `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `business_license` varchar(50) DEFAULT NULL COMMENT '营业执照号',
  `license_image` varchar(500) DEFAULT NULL COMMENT '营业执照图片',
  `company_address` varchar(200) DEFAULT NULL COMMENT '企业地址',
  `region_code` varchar(20) DEFAULT NULL COMMENT '地区编码',
  `company_scale` varchar(50) DEFAULT NULL COMMENT '企业规模',
  `company_nature` varchar(50) DEFAULT NULL COMMENT '企业性质',
  `company_description` text COMMENT '企业描述',
  `logo_url` varchar(500) DEFAULT NULL COMMENT '企业logo',
  `audit_status` tinyint(1) DEFAULT '0' COMMENT '审核状态（0待审核 1已通过 2已拒绝）',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_by` varchar(64) DEFAULT NULL COMMENT '审核人',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1禁用）',
  `login_account` varchar(50) NOT NULL COMMENT '登录账号',
  `login_password` varchar(100) NOT NULL COMMENT '登录密码',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`employer_id`),
  UNIQUE KEY `uk_login_account` (`login_account`),
  UNIQUE KEY `uk_contact_phone` (`contact_phone`),
  KEY `idx_company_name` (`company_name`),
  KEY `idx_region_code` (`region_code`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家表';

-- 3. 职位表
DROP TABLE IF EXISTS `rec_job_position`;
CREATE TABLE `rec_job_position` (
  `position_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '职位ID',
  `employer_id` bigint(20) NOT NULL COMMENT '商家ID',
  `position_title` varchar(100) NOT NULL COMMENT '职位标题',
  `position_type` varchar(50) DEFAULT NULL COMMENT '职位类型',
  `industry` varchar(50) DEFAULT NULL COMMENT '所属行业',
  `work_nature` varchar(20) DEFAULT NULL COMMENT '工作性质（全职/兼职/实习）',
  `salary_min` decimal(10,2) DEFAULT NULL COMMENT '最低薪资',
  `salary_max` decimal(10,2) DEFAULT NULL COMMENT '最高薪资',
  `salary_unit` varchar(10) DEFAULT '月' COMMENT '薪资单位（月/日/小时）',
  `work_experience` varchar(50) DEFAULT NULL COMMENT '工作经验要求',
  `education` varchar(50) DEFAULT NULL COMMENT '学历要求',
  `work_address` varchar(200) DEFAULT NULL COMMENT '工作地址',
  `region_code` varchar(20) DEFAULT NULL COMMENT '地区编码',
  `job_description` text COMMENT '职位描述',
  `job_requirements` text COMMENT '职位要求',
  `welfare_benefits` text COMMENT '福利待遇',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_wechat` varchar(50) DEFAULT NULL COMMENT '微信号',
  `publish_status` tinyint(1) DEFAULT '0' COMMENT '发布状态（0草稿 1待审核 2已发布 3已下线）',
  `audit_status` tinyint(1) DEFAULT '0' COMMENT '审核状态（0待审核 1已通过 2已拒绝）',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_by` varchar(64) DEFAULT NULL COMMENT '审核人',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `view_count` int(11) DEFAULT '0' COMMENT '浏览次数',
  `apply_count` int(11) DEFAULT '0' COMMENT '申请次数',
  `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶（0否 1是）',
  `is_urgent` tinyint(1) DEFAULT '0' COMMENT '是否紧急（0否 1是）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`position_id`),
  KEY `idx_employer_id` (`employer_id`),
  KEY `idx_position_title` (`position_title`),
  KEY `idx_region_code` (`region_code`),
  KEY `idx_publish_status` (`publish_status`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_publish_time` (`publish_time`),
  KEY `idx_is_top` (`is_top`),
  KEY `idx_is_urgent` (`is_urgent`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='职位表';

-- 4. 订单表
DROP TABLE IF EXISTS `rec_order`;
CREATE TABLE `rec_order` (
  `order_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `seeker_id` bigint(20) NOT NULL COMMENT '求职者ID',
  `package_id` bigint(20) DEFAULT NULL COMMENT '套餐ID',
  `order_type` tinyint(1) NOT NULL COMMENT '订单类型（1单次查看 2会员套餐）',
  `order_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
  `order_status` tinyint(1) DEFAULT '0' COMMENT '订单状态（0待支付 1已支付 2已取消 3已退款）',
  `pay_status` tinyint(1) DEFAULT '0' COMMENT '支付状态（0未支付 1已支付 2支付失败）',
  `pay_type` varchar(20) DEFAULT 'wechat_pay' COMMENT '支付方式',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '微信支付交易号',
  `view_count` int(11) DEFAULT '0' COMMENT '获得查看次数',
  `expire_time` datetime DEFAULT NULL COMMENT '订单过期时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_seeker_id` (`seeker_id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 5. 会员套餐表
DROP TABLE IF EXISTS `rec_member_package`;
CREATE TABLE `rec_member_package` (
  `package_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '套餐ID',
  `package_name` varchar(100) NOT NULL COMMENT '套餐名称',
  `package_type` tinyint(1) NOT NULL COMMENT '套餐类型（1单次查看 2会员套餐）',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `view_count` int(11) DEFAULT '0' COMMENT '查看次数（0表示无限制）',
  `validity_days` int(11) DEFAULT '0' COMMENT '有效天数（0表示永久）',
  `description` text COMMENT '套餐描述',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用（0否 1是）',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`package_id`),
  KEY `idx_package_type` (`package_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员套餐表';

-- 6. 查看记录表
DROP TABLE IF EXISTS `rec_view_record`;
CREATE TABLE `rec_view_record` (
  `record_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `seeker_id` bigint(20) NOT NULL COMMENT '求职者ID',
  `position_id` bigint(20) NOT NULL COMMENT '职位ID',
  `employer_id` bigint(20) NOT NULL COMMENT '商家ID',
  `view_type` tinyint(1) NOT NULL COMMENT '查看类型（1单次付费 2会员查看）',
  `cost_amount` decimal(10,2) DEFAULT '0.00' COMMENT '消费金额',
  `order_id` bigint(20) DEFAULT NULL COMMENT '关联订单ID',
  `view_time` datetime NOT NULL COMMENT '查看时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`record_id`),
  KEY `idx_seeker_id` (`seeker_id`),
  KEY `idx_position_id` (`position_id`),
  KEY `idx_employer_id` (`employer_id`),
  KEY `idx_view_time` (`view_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='查看记录表';

-- 7. 地区表
DROP TABLE IF EXISTS `rec_region`;
CREATE TABLE `rec_region` (
  `region_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '地区ID',
  `region_code` varchar(20) NOT NULL COMMENT '地区编码',
  `region_name` varchar(50) NOT NULL COMMENT '地区名称',
  `parent_code` varchar(20) DEFAULT NULL COMMENT '父级地区编码',
  `region_level` tinyint(1) NOT NULL COMMENT '地区级别（1省 2市 3区县）',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用（0否 1是）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`region_id`),
  UNIQUE KEY `uk_region_code` (`region_code`),
  KEY `idx_parent_code` (`parent_code`),
  KEY `idx_region_level` (`region_level`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区表';

-- 8. 系统配置表
DROP TABLE IF EXISTS `rec_system_config`;
CREATE TABLE `rec_system_config` (
  `config_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(50) DEFAULT 'string' COMMENT '配置类型',
  `config_desc` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统配置（0否 1是）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`config_id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_type` (`config_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
