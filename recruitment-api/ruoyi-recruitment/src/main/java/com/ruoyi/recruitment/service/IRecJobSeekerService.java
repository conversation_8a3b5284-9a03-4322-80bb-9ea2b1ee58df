package com.ruoyi.recruitment.service;

import java.util.List;
import com.ruoyi.recruitment.domain.RecJobSeeker;

/**
 * 求职者Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IRecJobSeekerService 
{
    /**
     * 查询求职者
     * 
     * @param seekerId 求职者主键
     * @return 求职者
     */
    public RecJobSeeker selectRecJobSeekerBySeekerId(Long seekerId);

    /**
     * 根据openid查询求职者
     * 
     * @param openid 微信openid
     * @return 求职者
     */
    public RecJobSeeker selectRecJobSeekerByOpenid(String openid);

    /**
     * 根据手机号查询求职者
     * 
     * @param phone 手机号
     * @return 求职者
     */
    public RecJobSeeker selectRecJobSeekerByPhone(String phone);

    /**
     * 查询求职者列表
     * 
     * @param recJobSeeker 求职者
     * @return 求职者集合
     */
    public List<RecJobSeeker> selectRecJobSeekerList(RecJobSeeker recJobSeeker);

    /**
     * 新增求职者
     * 
     * @param recJobSeeker 求职者
     * @return 结果
     */
    public int insertRecJobSeeker(RecJobSeeker recJobSeeker);

    /**
     * 修改求职者
     * 
     * @param recJobSeeker 求职者
     * @return 结果
     */
    public int updateRecJobSeeker(RecJobSeeker recJobSeeker);

    /**
     * 批量删除求职者
     * 
     * @param seekerIds 需要删除的求职者主键集合
     * @return 结果
     */
    public int deleteRecJobSeekerBySeekerIds(Long[] seekerIds);

    /**
     * 删除求职者信息
     * 
     * @param seekerId 求职者主键
     * @return 结果
     */
    public int deleteRecJobSeekerBySeekerId(Long seekerId);

    /**
     * 微信授权登录
     * 
     * @param code 微信授权码
     * @return 求职者信息
     */
    public RecJobSeeker wechatLogin(String code);

    /**
     * 手机号一键登录
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 求职者信息
     */
    public RecJobSeeker phoneLogin(String phone, String code);

    /**
     * 注册求职者
     * 
     * @param recJobSeeker 求职者信息
     * @return 结果
     */
    public int registerSeeker(RecJobSeeker recJobSeeker);

    /**
     * 更新求职者基本信息
     * 
     * @param recJobSeeker 求职者信息
     * @return 结果
     */
    public int updateSeekerProfile(RecJobSeeker recJobSeeker);

    /**
     * 检查求职者是否为会员
     * 
     * @param seekerId 求职者ID
     * @return 是否为会员
     */
    public boolean isMember(Long seekerId);

    /**
     * 检查求职者剩余查看次数
     * 
     * @param seekerId 求职者ID
     * @return 剩余次数
     */
    public int getRemainViewCount(Long seekerId);

    /**
     * 消费查看次数
     * 
     * @param seekerId 求职者ID
     * @param count 消费次数
     * @return 结果
     */
    public boolean consumeViewCount(Long seekerId, int count);

    /**
     * 增加查看次数
     * 
     * @param seekerId 求职者ID
     * @param count 增加次数
     * @return 结果
     */
    public boolean addViewCount(Long seekerId, int count);

    /**
     * 升级会员
     * 
     * @param seekerId 求职者ID
     * @param memberType 会员类型
     * @param viewCount 查看次数
     * @param validityDays 有效天数
     * @return 结果
     */
    public boolean upgradeMember(Long seekerId, Integer memberType, Integer viewCount, Integer validityDays);

    /**
     * 冻结/解冻求职者
     * 
     * @param seekerId 求职者ID
     * @param status 状态
     * @return 结果
     */
    public int updateSeekerStatus(Long seekerId, String status);

    /**
     * 获取求职者统计信息
     * 
     * @return 统计信息
     */
    public Object getSeekerStatistics();
}
