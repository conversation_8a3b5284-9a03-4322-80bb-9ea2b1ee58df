# 招聘平台系统

基于若依框架开发的招聘平台系统，支持小程序端、商家端和管理端三端应用。

## 项目简介

本项目是一个完整的招聘平台解决方案，包含以下功能：

- **小程序端**：求职者使用，支持微信登录、职位浏览、联系方式查看、会员购买等
- **商家端**：企业使用，支持职位发布、管理、审核状态查看等
- **管理端**：平台管理，支持用户管理、商家审核、职位审核、订单管理等

## 技术栈

### 后端技术
- **框架**：Spring Boot 2.5.x + Spring Security + JWT
- **ORM**：MyBatis-Plus
- **数据库**：MySQL 8.0
- **缓存**：Redis
- **文档**：Swagger2
- **日志**：Logback
- **工具**：Hutool、Jackson、Validation

### 前端技术
- **管理端**：Vue 2.x + Element UI + Axios
- **小程序端**：uni-app + Vue 2.x
- **构建工具**：Webpack、Vite

## 项目结构

```
recruitment-platform/
├── recruitment-api/                 # 后端API服务
│   ├── ruoyi-admin/                # 管理模块
│   ├── ruoyi-common/               # 通用模块
│   ├── ruoyi-framework/            # 框架模块
│   ├── ruoyi-generator/            # 代码生成
│   ├── ruoyi-quartz/               # 定时任务
│   ├── ruoyi-system/               # 系统模块
│   └── ruoyi-recruitment/          # 招聘业务模块
├── recruitment-ui/                  # 管理端前端
│   ├── src/
│   │   ├── api/                    # API接口
│   │   ├── assets/                 # 静态资源
│   │   ├── components/             # 公共组件
│   │   ├── layout/                 # 布局组件
│   │   ├── router/                 # 路由配置
│   │   ├── store/                  # 状态管理
│   │   ├── utils/                  # 工具类
│   │   └── views/                  # 页面组件
│   └── package.json
├── recruitment-miniapp/             # 小程序端
│   ├── pages/                      # 页面
│   ├── components/                 # 组件
│   ├── static/                     # 静态资源
│   ├── utils/                      # 工具类
│   └── manifest.json
├── sql/                            # 数据库脚本
│   ├── init_database.sql           # 数据库初始化
│   ├── recruitment_tables.sql      # 表结构
│   └── recruitment_data.sql        # 初始数据
└── docs/                           # 项目文档
    ├── api/                        # API文档
    ├── deploy/                     # 部署文档
    └── design/                     # 设计文档
```

## 核心功能

### 求职者功能
- 微信授权登录
- 职位搜索和浏览
- 查看职位详情
- 付费查看联系方式
- 会员套餐购买
- 个人信息管理
- 消费记录查看

### 商家功能
- 商家注册和认证
- 职位发布和管理
- 职位审核状态查看
- 企业信息管理
- 数据统计查看

### 管理功能
- 用户管理（求职者、商家）
- 职位审核和管理
- 订单管理
- 会员套餐管理
- 系统配置管理
- 数据统计分析

## 快速开始

### 环境要求
- JDK 1.8+
- MySQL 8.0+
- Redis 5.0+
- Node.js 14+
- Maven 3.6+

### 后端启动

1. 克隆项目
```bash
git clone https://github.com/your-repo/recruitment-platform.git
cd recruitment-platform
```

2. 创建数据库
```bash
mysql -u root -p < sql/init_database.sql
```

3. 修改配置文件
```bash
# 编辑 recruitment-api/ruoyi-admin/src/main/resources/application-druid.yml
# 修改数据库连接信息

# 编辑 recruitment-api/ruoyi-admin/src/main/resources/application.yml  
# 修改Redis连接信息
```

4. 启动后端服务
```bash
cd recruitment-api
mvn clean install
cd ruoyi-admin
mvn spring-boot:run
```

### 前端启动

1. 安装依赖
```bash
cd recruitment-ui
npm install
```

2. 启动开发服务器
```bash
npm run dev
```

3. 构建生产版本
```bash
npm run build:prod
```

### 小程序启动

1. 安装依赖
```bash
cd recruitment-miniapp
npm install
```

2. 使用HBuilderX打开项目，运行到微信小程序开发者工具

## 配置说明

### 微信小程序配置
在系统配置中设置以下参数：
- `wechat.appid`：微信小程序AppID
- `wechat.secret`：微信小程序Secret
- `wechat.mch_id`：微信商户号
- `wechat.api_key`：微信支付API密钥

### 支付配置
- `single_view_price`：单次查看价格
- 会员套餐价格在会员套餐管理中配置

## API文档

启动后端服务后，访问 http://localhost:8080/swagger-ui.html 查看API文档。

## 部署指南

详细部署指南请参考 [部署文档](docs/deploy/README.md)

## 开发指南

详细开发指南请参考 [开发文档](docs/development/README.md)

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目地址：https://github.com/your-repo/recruitment-platform
- 问题反馈：https://github.com/your-repo/recruitment-platform/issues
- 邮箱：<EMAIL>

## 更新日志

### v1.0.0 (2025-01-01)
- 初始版本发布
- 实现基础的招聘平台功能
- 支持三端应用（小程序、商家、管理）
- 集成微信支付功能
- 完善的权限管理系统
